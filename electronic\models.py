from pydantic import BaseModel
from typing import Dict, Any, List, Union


class ElectronicCommandsResponse(BaseModel):
    """Response model for /electronic/get_commands endpoint"""
    hardware_version: int
    commands: Dict[str, Dict[str, Any]]


class ElectronicCommandRequest(BaseModel):
    """Request model for /electronic/command endpoint"""
    command_name: str
    params: List[Union[str, int, float]]


class ElectronicCommandResponse(BaseModel):
    """Response model for /electronic/command endpoint"""
    success: bool
    result: Any = None
    error: str = None
