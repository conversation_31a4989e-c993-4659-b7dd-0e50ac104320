import asyncio
import subprocess
import logging
import os
from typing import Dict, Any, List, Union
from fastapi import APIRouter, HTTPException
from operator.models import ElectronicCommandsResponse, ElectronicCommandRequest, ElectronicCommandResponse
from config import device_config
from os import getenv

router = APIRouter()
logger = logging.getLogger(__name__)

# Hardware version from config
HARDWARE_VERSION = device_config.fsm_config["hardware_version"]

# Path to boardctl.py
SCRIPT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
BOARDCTL_PATH = os.path.join(SCRIPT_DIR, "hardware", "boardctl.py")
PYTHON_ALIAS = "py"

# Command definitions from boardctl.py with parameter specifications
ELECTRONIC_COMMANDS = {
    "unlock": {
        "description": "Unlock a specific box door",
        "parameters": [
            {"name": "lock_id", "type": "int", "min": 1, "max": 206, "description": "Lock ID (1-206)"}
        ]
    },
    "lock_tempered": {
        "description": "Lock all tempered doors",
        "parameters": []
    },
    "unlock_tempered": {
        "description": "Unlock a specific tempered box door",
        "parameters": [
            {"name": "lock_id", "type": "int", "min": 1, "max": 206, "description": "Lock ID (1-206)"}
        ]
    },
    "unlock_service": {
        "description": "Unlock service door",
        "parameters": []
    },
    "unlock_service_test": {
        "description": "Unlock service door (test mode)",
        "parameters": []
    },
    "write_lock_timeout": {
        "description": "Set lock timeout configuration",
        "parameters": [
            {"name": "timeout", "type": "int", "min": 15, "max": 300, "description": "Timeout in seconds (15-300)"}
        ]
    },
    "read_lock_timeout": {
        "description": "Read lock timeout configuration",
        "parameters": []
    },
    "read_lock_state": {
        "description": "Read state of a specific lock",
        "parameters": [
            {"name": "lock_id", "type": "int", "min": 1, "max": 206, "description": "Lock ID (1-206)"}
        ]
    },
    "read_service_lock_state": {
        "description": "Read service lock state",
        "parameters": []
    },
    "enable_service_lock": {
        "description": "Enable/disable service lock",
        "parameters": [
            {"name": "enable1", "type": "bool", "description": "First enable flag"},
            {"name": "enable2", "type": "bool", "description": "Second enable flag"}
        ]
    },
    "read_security_state": {
        "description": "Read security input state",
        "parameters": []
    },
    "clear_security_state": {
        "description": "Clear security input state",
        "parameters": []
    },
    "enable_security": {
        "description": "Enable security input",
        "parameters": []
    },
    "disable_security": {
        "description": "Disable security input",
        "parameters": []
    },
    "read_ups_state": {
        "description": "Read UPS input state",
        "parameters": []
    },
    "clear_ups_state": {
        "description": "Clear UPS input state",
        "parameters": []
    },
    "enable_ups": {
        "description": "Enable UPS input",
        "parameters": []
    },
    "disable_ups": {
        "description": "Disable UPS input",
        "parameters": []
    },
    "read_temperature": {
        "description": "Read temperature from a channel",
        "parameters": [
            {"name": "channel", "type": "int", "min": 1, "max": 8, "description": "Temperature channel (1-8)"}
        ]
    },
    "write_main_heating_config": {
        "description": "Configure main heating parameters",
        "parameters": [
            {"name": "heating_id", "type": "int", "min": 1, "max": 2, "description": "Heating ID (1-2)"},
            {"name": "temp_point", "type": "float", "min": -30.0, "max": 30.0, "description": "Temperature point (-30 to 30°C)"},
            {"name": "temp_diff1", "type": "float", "min": 0.0, "max": 10.0, "description": "Temperature difference 1 (0-10°C)"},
            {"name": "temp_diff2", "type": "float", "min": 0.0, "max": 10.0, "description": "Temperature difference 2 (0-10°C)"},
            {"name": "temp_neutral", "type": "float", "min": 0.0, "max": 20.0, "description": "Neutral temperature (0-20°C)"}
        ]
    },
    "read_main_heating_config": {
        "description": "Read main heating configuration",
        "parameters": [
            {"name": "heating_id", "type": "int", "min": 1, "max": 2, "description": "Heating ID (1-2)"}
        ]
    },
    "write_service_heating_config": {
        "description": "Configure service heating parameters",
        "parameters": [
            {"name": "temp_minimum", "type": "float", "min": -10.0, "max": 30.0, "description": "Minimum temperature (-10 to 30°C)"},
            {"name": "temp_diff", "type": "float", "min": 0.0, "max": 10.0, "description": "Temperature difference (0-10°C)"}
        ]
    },
    "read_service_heating_config": {
        "description": "Read service heating configuration",
        "parameters": []
    },
    "write_service_fan_config": {
        "description": "Configure service fan parameters",
        "parameters": [
            {"name": "temp_maximum", "type": "float", "min": 0.0, "max": 70.0, "description": "Maximum temperature (0-70°C)"},
            {"name": "temp_diff", "type": "float", "min": 0.0, "max": 10.0, "description": "Temperature difference (0-10°C)"}
        ]
    },
    "read_service_fan_config": {
        "description": "Read service fan configuration",
        "parameters": []
    },
    "write_rear_fan_config": {
        "description": "Configure rear fan timing",
        "parameters": [
            {"name": "fan_id", "type": "int", "min": 1, "max": 4, "description": "Fan ID (1-4)"},
            {"name": "time1", "type": "int", "min": 0, "max": 600, "description": "Time 1 (0-600 seconds)"},
            {"name": "time2", "type": "int", "min": 0, "max": 600, "description": "Time 2 (0-600 seconds)"}
        ]
    },
    "read_rear_fan_config": {
        "description": "Read rear fan configuration",
        "parameters": [
            {"name": "fan_id", "type": "int", "min": 1, "max": 4, "description": "Fan ID (1-4)"}
        ]
    },
    "write_fan_current_config": {
        "description": "Configure fan current limits",
        "parameters": [
            {"name": "fan_id", "type": "int", "min": 1, "max": 4, "description": "Fan ID (1-4)"},
            {"name": "current1", "type": "int", "min": 0, "max": 1000, "description": "Current limit 1 (0-1000mA)"},
            {"name": "current2", "type": "int", "min": 0, "max": 1000, "description": "Current limit 2 (0-1000mA)"}
        ]
    },
    "read_fan_current_config": {
        "description": "Read fan current configuration",
        "parameters": [
            {"name": "fan_id", "type": "int", "min": 1, "max": 4, "description": "Fan ID (1-4)"}
        ]
    },
    "test_fan": {
        "description": "Test fan operation",
        "parameters": [
            {"name": "fan_id", "type": "int", "min": 1, "max": 4, "description": "Fan ID (1-4)"},
            {"name": "mode", "type": "int", "min": 0, "max": 2, "description": "Test mode (0-2)"}
        ]
    },
    "enable_led": {
        "description": "Enable LED system",
        "parameters": []
    },
    "disable_led": {
        "description": "Disable LED system",
        "parameters": []
    },
    "read_led_state": {
        "description": "Read LED system state",
        "parameters": []
    },
    "clear_all_led": {
        "description": "Clear all LED colors",
        "parameters": []
    },
    "write_led_colors": {
        "description": "Set LED colors for specific locks",
        "parameters": [
            {"name": "lock_id", "type": "int", "min": 1, "max": 206, "description": "Lock ID (1-206)"},
            {"name": "color", "type": "str", "description": "Color name (A, B, C, D or none)"}
        ]
    },
    "write_segment_config": {
        "description": "Configure LED segment",
        "parameters": [
            {"name": "lock_id", "type": "int", "min": 1, "max": 206, "description": "Lock ID (1-206)"},
            {"name": "strip", "type": "int", "min": 1, "max": 4, "description": "LED strip (1-4)"},
            {"name": "led_start", "type": "int", "min": 1, "max": 120, "description": "Start LED (1-120)"},
            {"name": "led_end", "type": "int", "min": 1, "max": 120, "description": "End LED (1-120)"}
        ]
    },
    "read_segment_config": {
        "description": "Read LED segment configuration",
        "parameters": [
            {"name": "lock_id", "type": "int", "min": 1, "max": 206, "description": "Lock ID (1-206)"}
        ]
    },
    "write_color_config": {
        "description": "Configure LED color RGB values",
        "parameters": [
            {"name": "color", "type": "str", "description": "Color name (A, B, C, D)"},
            {"name": "red", "type": "int", "min": 0, "max": 255, "description": "Red value (0-255)"},
            {"name": "green", "type": "int", "min": 0, "max": 255, "description": "Green value (0-255)"},
            {"name": "blue", "type": "int", "min": 0, "max": 255, "description": "Blue value (0-255)"}
        ]
    },
    "read_color_config": {
        "description": "Read LED color configuration",
        "parameters": [
            {"name": "color", "type": "str", "description": "Color name (A, B, C, D)"}
        ]
    },
    "read_inner_light_state": {
        "description": "Read inner light state",
        "parameters": []
    },
    "enable_inner_light": {
        "description": "Enable inner light",
        "parameters": []
    },
    "disable_inner_light": {
        "description": "Disable inner light",
        "parameters": []
    },
    "read_outer_light_state": {
        "description": "Read outer light state",
        "parameters": []
    },
    "enable_outer_light": {
        "description": "Enable outer light",
        "parameters": []
    },
    "disable_outer_light": {
        "description": "Disable outer light",
        "parameters": []
    },
    "write_outer_light_config": {
        "description": "Configure outer light timeout",
        "parameters": [
            {"name": "timeout", "type": "int", "min": 0, "max": 300, "description": "Timeout in seconds (0-300)"}
        ]
    },
    "read_outer_light_config": {
        "description": "Read outer light configuration",
        "parameters": []
    },
    "write_inner_light_timeout": {
        "description": "Configure inner light timeout",
        "parameters": [
            {"name": "timeout", "type": "int", "min": 5, "max": 300, "description": "Timeout in seconds (5-300)"}
        ]
    },
    "read_inner_light_timeout": {
        "description": "Read inner light timeout configuration",
        "parameters": []
    },
    "test_light": {
        "description": "Test light operation",
        "parameters": [
            {"name": "light_id", "type": "int", "min": 1, "max": 3, "description": "Light ID (1-3)"},
            {"name": "mode", "type": "int", "min": 0, "max": 2, "description": "Test mode (0-2)"}
        ]
    },
    "read_input": {
        "description": "Read input state",
        "parameters": [
            {"name": "input_id", "type": "int", "min": 1, "max": 8, "description": "Input ID (1-8)"}
        ]
    },
    "clear_input": {
        "description": "Clear input state",
        "parameters": [
            {"name": "input_id", "type": "int", "min": 1, "max": 8, "description": "Input ID (1-8)"}
        ]
    },
    "enable_input": {
        "description": "Enable input",
        "parameters": [
            {"name": "input_id", "type": "int", "min": 1, "max": 8, "description": "Input ID (1-8)"}
        ]
    },
    "disable_input": {
        "description": "Disable input",
        "parameters": [
            {"name": "input_id", "type": "int", "min": 1, "max": 8, "description": "Input ID (1-8)"}
        ]
    },
    "read_output": {
        "description": "Read output state",
        "parameters": [
            {"name": "output_id", "type": "int", "min": 1, "max": 15, "description": "Output ID (1-15)"}
        ]
    },
    "write_output": {
        "description": "Set output state",
        "parameters": [
            {"name": "output_id", "type": "int", "min": 1, "max": 15, "description": "Output ID (1-15)"},
            {"name": "state", "type": "bool", "description": "Output state (true/false)"}
        ]
    },
    "test_output": {
        "description": "Test output operation",
        "parameters": [
            {"name": "output_id", "type": "int", "min": 1, "max": 15, "description": "Output ID (1-15)"},
            {"name": "mode", "type": "int", "min": 0, "max": 2, "description": "Test mode (0-2)"}
        ]
    },
    "read_error_register": {
        "description": "Read error register",
        "parameters": []
    },
    "clear_error_register": {
        "description": "Clear error register",
        "parameters": []
    },
    "firmware_version": {
        "description": "Get firmware version",
        "parameters": []
    },
    "hardware_version": {
        "description": "Get hardware version",
        "parameters": []
    },
    "firmware_restart": {
        "description": "Restart firmware",
        "parameters": []
    },
    "run_bootloader": {
        "description": "Run bootloader for firmware update",
        "parameters": []
    }
}


@router.get("/get_commands", response_model=ElectronicCommandsResponse)
async def get_electronic_commands():
    """
    Returns all available electronic commands with their parameters.
    """
    return ElectronicCommandsResponse(
        hardware_version=HARDWARE_VERSION,
        commands=ELECTRONIC_COMMANDS
    )


@router.post("/command", response_model=ElectronicCommandResponse)
async def execute_electronic_command(request: ElectronicCommandRequest):
    """
    Execute an electronic command via boardctl.py
    """
    try:
        # Validate command exists
        if request.command_name not in ELECTRONIC_COMMANDS:
            return ElectronicCommandResponse(
                success=False,
                error=f"Unknown command: {request.command_name}"
            )
        
        # Get command info
        command_info = ELECTRONIC_COMMANDS[request.command_name]
        expected_params = command_info["parameters"]
        
        # Validate parameter count
        if len(request.params) != len(expected_params):
            return ElectronicCommandResponse(
                success=False,
                error=f"Command '{request.command_name}' expects {len(expected_params)} parameters, got {len(request.params)}"
            )
        
        # Validate parameter types and ranges
        validated_params = []
        for i, (param, expected) in enumerate(zip(request.params, expected_params)):
            try:
                if expected["type"] == "int":
                    val = int(param)
                    if "min" in expected and val < expected["min"]:
                        return ElectronicCommandResponse(
                            success=False,
                            error=f"Parameter {expected['name']} must be >= {expected['min']}, got {val}"
                        )
                    if "max" in expected and val > expected["max"]:
                        return ElectronicCommandResponse(
                            success=False,
                            error=f"Parameter {expected['name']} must be <= {expected['max']}, got {val}"
                        )
                    validated_params.append(str(val))
                elif expected["type"] == "float":
                    val = float(param)
                    if "min" in expected and val < expected["min"]:
                        return ElectronicCommandResponse(
                            success=False,
                            error=f"Parameter {expected['name']} must be >= {expected['min']}, got {val}"
                        )
                    if "max" in expected and val > expected["max"]:
                        return ElectronicCommandResponse(
                            success=False,
                            error=f"Parameter {expected['name']} must be <= {expected['max']}, got {val}"
                        )
                    validated_params.append(str(val))
                elif expected["type"] == "bool":
                    if isinstance(param, bool):
                        validated_params.append("1" if param else "0")
                    elif str(param).lower() in ["true", "1", "on", "yes"]:
                        validated_params.append("1")
                    elif str(param).lower() in ["false", "0", "off", "no"]:
                        validated_params.append("0")
                    else:
                        return ElectronicCommandResponse(
                            success=False,
                            error=f"Parameter {expected['name']} must be boolean, got {param}"
                        )
                elif expected["type"] == "str":
                    validated_params.append(str(param))
                else:
                    validated_params.append(str(param))
            except (ValueError, TypeError) as e:
                return ElectronicCommandResponse(
                    success=False,
                    error=f"Invalid parameter {expected['name']}: {str(e)}"
                )
        
        # Execute the command
        result = await execute_boardctl_command(request.command_name, validated_params)
        
        return ElectronicCommandResponse(
            success=True,
            result=result
        )
        
    except Exception as e:
        logger.error(f"Error executing command {request.command_name}: {str(e)}")
        return ElectronicCommandResponse(
            success=False,
            error=f"Internal error: {str(e)}"
        )


async def execute_boardctl_command(command_name: str, params: List[str]) -> str:
    """
    Execute a boardctl.py command with the given parameters.
    """
    try:
        # Get hardware port from config
        hardware_port = device_config.fsm_config["hardware_port"]
        
        # Build command
        cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", command_name] + params
        
        logger.info(f"Executing command: {' '.join(cmd)}")
        
        # Execute command
        def run_command():
            try:
                proc = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=os.path.dirname(BOARDCTL_PATH)
                )
                stdout, stderr = proc.communicate(timeout=10)
                return stdout, stderr, proc.returncode
            except subprocess.TimeoutExpired:
                proc.kill()
                return b"Command timeout", b"", -1
            except Exception as e:
                return str(e).encode(), b"", -1
        
        loop = asyncio.get_running_loop()
        stdout, stderr, returncode = await loop.run_in_executor(None, run_command)
        
        stdout_text = stdout.decode().strip()
        stderr_text = stderr.decode().strip()
        
        logger.info(f"Command output - stdout: {stdout_text}")
        if stderr_text:
            logger.info(f"Command output - stderr: {stderr_text}")
        
        # Parse result based on return code and output
        if returncode == 0 or "Script finished successfuly" in stdout_text:
            # Success - return the output
            lines = stdout_text.split('\n')
            # Return the last meaningful line (usually the result)
            for line in reversed(lines):
                line = line.strip()
                if line and not line.startswith("Script finished"):
                    return line
            return "1"  # Default success
        else:
            # Error - return error code or message
            if stdout_text:
                lines = stdout_text.split('\n')
                last_line = lines[-1].strip() if lines else ""
                if last_line.startswith('-'):
                    return last_line  # Error code
            return f"Command failed with return code {returncode}"
            
    except Exception as e:
        logger.error(f"Error executing boardctl command: {str(e)}")
        return f"Execution error: {str(e)}"
